extends Node
class_name BaseComponent

# Base class for all entity components

var entity: Entity
var initialized: bool = false

func _ready() -> void:
    # Get the entity (parent)
    entity = get_parent()
    
    # Initialize the component
    _initialize()

func _initialize() -> void:
    if initialized:
        return
    
    _on_initialize()
    initialized = true

func _process(delta: float) -> void:
    if initialized:
        _on_update(delta)

func _physics_process(delta: float) -> void:
    if initialized:
        _on_physics_update(delta)

# Virtual methods to override
func _on_initialize() -> void:
    pass

func _on_update(delta: float) -> void:
    pass

func _on_physics_update(delta: float) -> void:
    pass

func _on_destroy() -> void:
    pass