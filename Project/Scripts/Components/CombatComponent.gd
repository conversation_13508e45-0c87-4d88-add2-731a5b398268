extends BaseComponent
class_name CombatComponent

signal attack_started(move_name)
signal attack_finished(move_name)
signal ability_used(ability_name)
signal ability_finished(ability_name)
signal health_changed(current_health, max_health)
signal died

# Combat stats
@export var attack_power: float = 10.0
@export var defense: float = 5.0
@export var critical_chance: float = 0.05
@export var critical_multiplier: float = 1.5

# Health
@export var max_health: float = 100.0
var current_health: float = max_health

# Moves
var available_moves: Dictionary = {}
var current_move = null
var is_attacking: bool = false

# Abilities
var available_abilities: Dictionary = {}
var ability_cooldowns: Dictionary = {}
var is_using_ability: bool = false

# References
var animation_player: AnimationPlayer
var state_component: StateComponent
var movement_component: MovementComponent
var resource_component: ResourceComponent

func _on_initialize():
    current_health = max_health
    
    # Register default moves
    register_default_moves()
    
    # Register default abilities
    register_default_abilities()
    
    # Get references to other components
    animation_player = entity.get_node_or_null("AnimationPlayer")
    state_component = entity.get_component("StateComponent")
    movement_component = entity.get_component("MovementComponent")
    resource_component = entity.get_component("ResourceComponent")

func _on_update(delta):
    # Update ability cooldowns
    for ability_name in ability_cooldowns.keys():
        if ability_cooldowns[ability_name] > 0:
            ability_cooldowns[ability_name] -= delta
            if ability_cooldowns[ability_name] <= 0:
                ability_cooldowns[ability_name] = 0

func register_default_moves():
    # Light attacks
    register_move("light_upper", LightUpperMove.new())
    register_move("light_lower", LightLowerMove.new())
    
    # Heavy attacks
    register_move("heavy_upper", HeavyUpperMove.new())
    register_move("heavy_lower", HeavyLowerMove.new())
    
    # Defensive moves
    register_move("block", BlockMove.new())
    register_move("dodge", DodgeRollMove.new())
    register_move("dash", DashMove.new())
    register_move("glide", GlideMove.new())

func register_default_abilities():
    # Ranged abilities
    register_ability("fireball", RangedAbility.new("fireball", self, 15.0, 5.0))
    register_ability("ice_spike", RangedAbility.new("ice_spike", self, 20.0, 8.0))
    
    # Close range abilities
    register_ability("whirlwind", CloseAbility.new("whirlwind", self, 25.0, 10.0))
    register_ability("ground_slam", CloseAbility.new("ground_slam", self, 30.0, 12.0))
    
    # AOE abilities
    register_ability("flame_burst", AOEAbility.new("flame_burst", self, 35.0, 15.0))
    register_ability("frost_nova", AOEAbility.new("frost_nova", self, 40.0, 20.0))
    
    # Self abilities
    register_ability("heal", SelfAbility.new("heal", self, 0.0, 30.0))
    register_ability("speed_boost", SelfAbility.new("speed_boost", self, 0.0, 25.0))

func register_move(move_name: String, move_strategy):
    available_moves[move_name] = move_strategy

func register_ability(ability_name: String, ability):
    available_abilities[ability_name] = ability
    ability_cooldowns[ability_name] = 0.0

func execute_move(move_name: String) -> bool:
    if is_attacking or is_using_ability:
        return false
        
    if not available_moves.has(move_name):
        push_warning("Move not found: " + move_name)
        return false
    
    current_move = available_moves[move_name]
    is_attacking = true
    attack_started.emit(move_name)
    
    var success = current_move.execute()
    
    if not success:
        is_attacking = false
        current_move = null
    
    return success

func finish_move():
    if current_move and is_attacking:
        var move_name = current_move.get_name()
        is_attacking = false
        current_move = null
        attack_finished.emit(move_name)

func use_ability(ability_name: String, target = null) -> bool:
    if is_attacking or is_using_ability:
        return false
        
    if not available_abilities.has(ability_name):
        push_warning("Ability not found: " + ability_name)
        return false
    
    # Check cooldown
    if ability_cooldowns[ability_name] > 0:
        push_warning("Ability on cooldown: " + ability_name)
        return false
    
    # Check mana cost
    var ability = available_abilities[ability_name]
    if resource_component and ability.mana_cost > 0:
        if resource_component.get_mana() < ability.mana_cost:
            push_warning("Not enough mana for ability: " + ability_name)
            return false
    
    is_using_ability = true
    ability_used.emit(ability_name)
    
    var success = ability.use(target)
    
    if success and resource_component and ability.mana_cost > 0:
        resource_component.use_mana(ability.mana_cost)
        ability_cooldowns[ability_name] = ability.cooldown
    
    if not success:
        is_using_ability = false
    
    return success

func finish_ability(ability_name: String):
    if is_using_ability:
        is_using_ability = false
        ability_finished.emit(ability_name)

func take_damage(amount: float, attacker = null) -> float:
    var actual_damage = max(1, amount - defense * 0.5)
    current_health -= actual_damage
    health_changed.emit(current_health, max_health)
    
    if current_health <= 0:
        current_health = 0
        die()
    
    return actual_damage

func heal(amount: float) -> float:
    var actual_heal = min(amount, max_health - current_health)
    current_health += actual_heal
    health_changed.emit(current_health, max_health)
    return actual_heal

func die():
    died.emit()
    if entity:
        entity.queue_free()

func get_health_percentage() -> float:
    return current_health / max_health * 100.0
