extends BaseComponent
class_name StateComponent

signal state_changed(previous_state, new_state)

@export_group("States")
@export var available_states: Array[String] = ["grounded", "in_air", "attacking", "defending", "flying", "lucid_mode", "aiming"]

var current_state: String = "grounded"
var previous_state: String = ""
var state_time: float = 0.0

# State strategies
var state_strategies = {}

func _on_initialize():
	# Initialize state strategies
	for state in available_states:
		var strategy_script = load("res://Project/Scripts/States/" + state.capitalize() + "State.gd")
		if strategy_script:
			var strategy = strategy_script.new()
			strategy.component = self
			state_strategies[state] = strategy
	
	# Enter initial state
	change_state("grounded")

func _on_update(delta: float):
	state_time += delta
	
	# Update current state
	if state_strategies.has(current_state):
		state_strategies[current_state].update(delta)

func change_state(new_state: String):
	if new_state == current_state:
		return
	
	if state_strategies.has(new_state):
		# Exit current state
		if state_strategies.has(current_state):
			state_strategies[current_state].exit()
		
		# Update state tracking
		previous_state = current_state
		current_state = new_state
		state_time = 0.0
		
		# Enter new state
		state_strategies[current_state].enter()
		
		emit_signal("state_changed", previous_state, current_state)

func can_change_to(state_name: String) -> bool:
	if not state_strategies.has(state_name):
		return false
	
	if not state_strategies.has(current_state):
		return true
	
	return state_strategies[current_state].can_transition_to(state_name)

func get_state_time() -> float:
	return state_time
