extends BTAction
class_name GuardTask

@export var guard_position_var: StringName = &"guard_position"
@export var guard_radius: float = 10.0
@export var target_var: StringName = &"target"
@export var alert_distance: float = 15.0
@export var rotation_speed: float = 1.0

var original_position: Vector3
var current_rotation: float = 0.0
var rotation_direction: int = 1
var is_alerted: bool = false

func _enter():
    # Store original position if not set
    if not blackboard.has_var(guard_position_var):
        original_position = agent.global_position
        blackboard.set_var(guard_position_var, original_position)
    else:
        original_position = blackboard.get_var(guard_position_var)

func _tick(delta: float) -> Status:
    # Check for target
    var target = blackboard.get_var(target_var)
    if target and is_instance_valid(target):
        var distance_to_target = agent.global_position.distance_to(target.global_position)
        
        # If target is within alert distance, become alerted
        if distance_to_target <= alert_distance:
            is_alerted = true
            
            # Face the target
            var direction = target.global_position - agent.global_position
            direction.y = 0
            if direction.length() > 0.1:
                var target_rotation = atan2(direction.x, direction.z)
                agent.rotation.y = lerp_angle(agent.rotation.y, target_rotation, rotation_speed * delta)
            
            return SUCCESS  # Signal that we've detected something
    else:
        is_alerted = false
    
    # If we're too far from guard position, return to it
    var distance_to_origin = agent.global_position.distance_to(original_position)
    var movement_component
    if distance_to_origin > guard_radius:
        var direction = original_position - agent.global_position
        direction = direction.normalized()
        
        # Get movement component
        movement_component = agent.get_node_or_null("MovementComponent")
        if movement_component:
            movement_component.set_movement_vector(Vector2(direction.x, direction.z))
            movement_component.set_movement_type("walking")
        else:
            # Fallback movement
            agent.velocity = direction * 3.0
            agent.move_and_slide()
        
        # Face the direction we're moving
        var target_rotation = atan2(direction.x, direction.z)
        agent.rotation.y = lerp_angle(agent.rotation.y, target_rotation, rotation_speed * delta)
    else:
        # Stand still and look around
        if movement_component:
            movement_component.set_movement_vector(Vector2.ZERO)
            movement_component.set_movement_type("idle")
        else:
            agent.velocity = Vector3.ZERO
        
        # Rotate slowly to look around
        current_rotation += rotation_direction * rotation_speed * 0.5 * delta
        if randf() < 0.01:  # Occasionally change direction
            rotation_direction *= -1
        
        agent.rotation.y = current_rotation
    
    return RUNNING